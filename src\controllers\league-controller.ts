import { AddSingleFixtureData } from "@pro-clubs-manager/shared-dtos";
import { NextFunction, Request, Response } from "express";
import { inject, injectable } from "tsyringe";
import logger from "../config/logger";
import { ILeagueController, ILeagueService } from "../interfaces/league";
import { ImageService } from "../interfaces/util-services/image-service.interface";
import { ILeagueStatsService, ITeamLeagueService } from "../interfaces/wrapper-services";

@injectable()
export default class LeagueController implements ILeagueController {
  private leagueService: ILeagueService;
  private teamLeagueService: ITeamLeagueService;
  private leagueStatsService: ILeagueStatsService;
  private imageService: ImageService;

  constructor(
    @inject("ILeagueService") leagueService: ILeagueService,
    @inject("ITeamLeagueService") teamLeagueService: ITeamLeagueService,
    @inject("ILeagueStatsService") leagueStatsService: ILeagueStatsService,
    @inject("ImageService") imageService: ImageService
  ) {
    this.leagueService = leagueService;
    this.imageService = imageService;
    this.teamLeagueService = teamLeagueService;
    this.leagueStatsService = leagueStatsService;
  }

  async createLeague(req: Request, res: Response, next: NextFunction) {
    const { name } = req.body;
    if (!name) {
      res.status(400).json({ error: "Name is required" });
      return;
    }

    const file = req.file;

    let imgUrl = undefined;

    try {
      if (file) {
        imgUrl = await this.imageService.uploadImage(file);
      } else {
        res.sendStatus(200);
        return;
      }
      const league = await this.leagueService.addLeague(name, imgUrl);
      res.status(201).json(league);
    } catch (error: any) {
      next(error);
    }
  }

  async startNewSeason(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { id: leagueId } = req.params;
    const { startDate, endDate } = req.body;

    if (!leagueId || !startDate) {
      res.status(404).send({ message: "missing date for generate fixtures" });
      return;
    }

    try {
      await this.leagueService.startNewSeason(leagueId, startDate, endDate);
      res.status(200).json({ success: true });
    } catch (e) {
      next(e);
    }
  }

  async deleteLeague(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { id } = req.params;

    try {
      await this.leagueService.deleteLeague(id);
      res.sendStatus(204);
    } catch (error: any) {
      next(error);
    }
  }

  async getLeagueById(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { id } = req.params;

    try {
      const league = await this.leagueService.getLeagueById(id);
      res.json(league);
    } catch (error: any) {
      next(error);
    }
  }

  async getAllLeagues(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const leagues = await this.leagueService.getAllLeagues();
      res.json(leagues);
    } catch (error: any) {
      next(error);
    }
  }

  async getLeagueTable(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { id: leagueId } = req.params;

    if (!leagueId) {
      res.status(404).send({ message: "No league id provided" });
      return;
    }

    try {
      const leagueTable = await this.leagueService.getLeagueTable(leagueId);
      res.json(leagueTable);
    } catch (error: any) {
      next(error);
    }
  }

  async clearLeagueCache(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { id: leagueId } = req.params;

    if (!leagueId) {
      res.status(400).send({ message: "No league id provided" });
      return;
    }

    try {
      // Clear all league-related caches
      await this.leagueService.clearLeagueTableCache(leagueId);
      await this.leagueService.clearTopScorersCache(leagueId);
      await this.leagueService.clearAllTimeTopScorersCache(leagueId);
      await this.leagueService.clearAllTimeTopAssistersCache(leagueId);
      await this.leagueService.clearMostHattricksCache(leagueId);
      await this.leagueService.clearMostCleanSheetsCache(leagueId);

      res.json({
        message: "League cache cleared successfully",
        leagueId: leagueId,
        clearedCaches: ['leagueTable', 'topScorers', 'topAssists', 'allTimeTopScorers', 'allTimeTopAssisters', 'mostHattricks', 'mostCleanSheets']
      });
    } catch (error: any) {
      next(error);
    }
  }

  async syncPlayerStats(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { id: leagueId } = req.params;

    if (!leagueId) {
      res.status(400).send({ message: "No league id provided" });
      return;
    }

    try {
      logger.info(`Starting player stats sync for league ${leagueId}`);

      // Sync stored player stats with game data
      const syncResult = await this.leagueService.syncPlayerStatsWithGameData(leagueId);

      // Clear caches after sync to force refresh with updated data
      await this.leagueService.clearLeagueTableCache(leagueId);
      await this.leagueService.clearTopScorersCache(leagueId);
      await this.leagueService.clearAllTimeTopScorersCache(leagueId);
      await this.leagueService.clearAllTimeTopAssistersCache(leagueId);

      res.json({
        message: "Player stats sync completed successfully",
        leagueId: leagueId,
        updated: syncResult.updated,
        errors: syncResult.errors,
        clearedCaches: ['leagueTable', 'topScorers', 'topAssists', 'allTimeTopScorers', 'allTimeTopAssisters']
      });

      logger.info(`Player stats sync completed for league ${leagueId}: ${syncResult.updated} players updated`);
    } catch (error: any) {
      logger.error(`Error syncing player stats for league ${leagueId}:`, error);
      next(error);
    }
  }

  async getTopScorers(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { id: leagueId } = req.params;

    const limit = req.query.limit ? parseInt(req.query.limit as string) : 50;

    if (!leagueId) {
      res.status(404).send({ message: "No league id provided" });
      return;
    }

    try {
      // Use cached league service method instead of uncached league stats service
      const topScorers = await this.leagueService.getTopScorers(leagueId, limit);
      res.json(topScorers);

    } catch (error) {
      next(error);
    }
  }
  async getTopAssists(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { id: leagueId } = req.params;

    const limit = req.query.limit ? parseInt(req.query.limit as string) : 50;

    if (!leagueId) {
      res.status(404).send({ message: "No league id provided" });
      return;
    }

    try {
      // Use cached league service method instead of uncached league stats service
      const topAssists = await this.leagueService.getTopAssists(leagueId, limit);
      res.json(topAssists);
    } catch (error) {
      next(error);
    }
  }

  async getAllTimeTopScorers(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { id: leagueId } = req.params;
    const limit = req.query.limit ? parseInt(req.query.limit as string) : 50;

    if (!leagueId) {
      res.status(404).send({ message: "No league id provided" });
      return;
    }

    try {
      const allTimeTopScorers = await this.leagueStatsService.getAllTimeTopScorers(leagueId, limit);
      res.json(allTimeTopScorers);
    } catch (error) {
      next(error);
    }
  }

  async getAllTimeTopAssisters(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { id: leagueId } = req.params;
    const limit = req.query.limit ? parseInt(req.query.limit as string) : 50;

    if (!leagueId) {
      res.status(404).send({ message: "No league id provided" });
      return;
    }

    try {
      const allTimeTopAssisters = await this.leagueStatsService.getAllTimeTopAssisters(leagueId, limit);
      res.json(allTimeTopAssisters);
    } catch (error) {
      next(error);
    }
  }

  async getAllTimeTopAvgRatingByPosition(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { id: leagueId } = req.params;
    const { position } = req.params;
    const minimumGames = req.query.minimumGames ? parseInt(req.query.minimumGames as string) : 15;
    const limit = req.query.limit ? parseInt(req.query.limit as string) : 50;

    if (!leagueId || !position) {
      res.status(404).send({ message: "League id and position are required" });
      return;
    }

    try {
      const allTimeTopAvgRating = await this.leagueStatsService.getAllTimeTopAvgRatingByPosition(leagueId, position, minimumGames, limit);
      res.json(allTimeTopAvgRating);
    } catch (error) {
      next(error);
    }
  }

  async getMostHattricks(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { id: leagueId } = req.params;
    const limit = req.query.limit ? parseInt(req.query.limit as string) : 50;

    if (!leagueId) {
      res.status(404).send({ message: "No league id provided" });
      return;
    }

    try {
      const mostHattricks = await this.leagueStatsService.getMostHattricks(leagueId, limit);
      res.json(mostHattricks);
    } catch (error) {
      next(error);
    }
  }

  async getMostCleanSheets(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { id: leagueId } = req.params;
    const limit = req.query.limit ? parseInt(req.query.limit as string) : 50;

    if (!leagueId) {
      res.status(404).send({ message: "No league id provided" });
      return;
    }

    try {
      const mostCleanSheets = await this.leagueStatsService.getMostCleanSheets(leagueId, limit);
      res.json(mostCleanSheets);
    } catch (error) {
      next(error);
    }
  }

  async getMostWinningPercentageTeams(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { id: leagueId } = req.params;
    const minimumGames = req.query.minimumGames ? parseInt(req.query.minimumGames as string) : 10;
    const limit = req.query.limit ? parseInt(req.query.limit as string) : 50;

    if (!leagueId) {
      res.status(404).send({ message: "No league id provided" });
      return;
    }

    try {
      const mostWinningPercentageTeams = await this.leagueStatsService.getMostWinningPercentageTeams(leagueId, minimumGames, limit);
      res.json(mostWinningPercentageTeams);
    } catch (error) {
      next(error);
    }
  }

  async getMostWinningPercentagePlayers(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { id: leagueId } = req.params;
    const minimumGames = req.query.minimumGames ? parseInt(req.query.minimumGames as string) : 10;
    const limit = req.query.limit ? parseInt(req.query.limit as string) : 50;

    if (!leagueId) {
      res.status(404).send({ message: "No league id provided" });
      return;
    }

    try {
      const mostWinningPercentagePlayers = await this.leagueStatsService.getMostWinningPercentagePlayers(leagueId, minimumGames, limit);
      res.json(mostWinningPercentagePlayers);
    } catch (error) {
      next(error);
    }
  }

  async getTopAvgRating(req: Request, res: Response, next: NextFunction) {
    const { leagueId } = req.params;
    try {
      const topPlayers = await this.leagueStatsService.getLeagueTopAvgRatingPlayers(leagueId);
      res.json(topPlayers);
    } catch (error: any) {
      next(error);
    }
  }

  async getAdvancedPlayersStats(req: Request, res: Response, next: NextFunction) {
    const { leagueId } = req.params;
    try {
      const advancedStats = await this.leagueStatsService.getAdvancedLeaguePlayersStats(leagueId);
      res.json(advancedStats);
    } catch (error: any) {
      next(error);
    }
  }
  async getAdvancedTeamsStats(req: Request, res: Response, next: NextFunction) {
    const { id: leagueId } = req.params;
    try {
      const advancedStats = await this.leagueStatsService.getAdvancedLeagueTeamStats(leagueId);
      res.json(advancedStats);
    } catch (error: any) {
      next(error);
    }
  }
  async getTeamOfTheWeek(req: Request, res: Response, next: NextFunction) {
    const { id: leagueId } = req.params;

    const { startDate, endDate } = req.query;

    if (!startDate || !endDate || !leagueId) {
      res.status(400).send({ message: "Missing data" });
      return;
    }

    const start = new Date(startDate as string);
    const end = new Date(endDate as string);

    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      res.status(400).send({ message: "Invalid date format" });
      return;
    }

    if (end <= start) {
      res.status(400).send({ message: "End date must be after start date" });
      return;
    }

    try {
      const teamOfWeek = await this.leagueService.getLeagueTeamOfTheWeek(leagueId, start, end);
      res.json(teamOfWeek);
    } catch (error: any) {
      next(error);
    }
  }

  async removeTeamFromLeague(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { id: leagueId } = req.params;
    const { teamId } = req.body;

    if (!leagueId || !teamId) {
      res.status(404).send({ message: "Missing data" });
      return;
    }

    try {
      await this.teamLeagueService.removeTeamFromLeague(leagueId, teamId);
      res.sendStatus(204);
    } catch (error: any) {
      next(error);
    }
  }

  async addTeamToLeague(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { id: leagueId } = req.params;
    const { teamId } = req.body;

    if (!leagueId || !teamId) {
      res.status(404).send({ message: "Missing data" });
      return;
    }

    try {
      await this.teamLeagueService.addTeamToLeague(leagueId, teamId);
      res.sendStatus(204);
    } catch (error: any) {
      next(error);
    }
  }

  async createLeagueFixture(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { id: leagueId } = req.params;

    if (!leagueId) {
      res.status(404).send({ message: "No league id provided" });
      return;
    }

    const fixtureData = req.body as AddSingleFixtureData;

    try {
      const fixture = await this.leagueService.createFixture(leagueId, fixtureData);
      res.status(201).json(fixture);
    } catch (error) {
      next(error);
    }
  }

  async generateLeagueFixtures(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { id: leagueId } = req.params;

    const { startDate, fixturesPerWeek }: { startDate: string; fixturesPerWeek: number } = req.body;

    if (!leagueId || !startDate || !fixturesPerWeek) {
      res.status(404).send({ message: "missing date for generate fixtures" });
      return;
    }

    try {
      const fixtures = await this.leagueService.generateLeagueFixtures(leagueId, startDate, fixturesPerWeek);
      res.status(201).json(fixtures);
    } catch (error) {
      next(error);
    }
  }

  async deleteAllLeagueFixtures(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { id: leagueId } = req.params;

    if (!leagueId) {
      res.status(404).send({ message: "No league id provided" });
      return;
    }

    try {
      await this.leagueService.deleteAllLeagueFixtures(leagueId);
      res.sendStatus(204);
    } catch (error) {
      next(error);
    }
  }
}
