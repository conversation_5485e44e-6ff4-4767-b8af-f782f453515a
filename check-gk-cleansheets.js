const mongoose = require('mongoose');
require('dotenv').config();

async function checkGKCleanSheets() {
  try {
    const runMode = process.env.RUN_MODE || 'prod';
    const dbName = runMode === 'prod' ? process.env.MONGODB_DB_PROD : process.env.MONGODB_DB_DEV;
    const mongoUri = `mongodb+srv://${process.env.MONGO_DB_USERNAME}:${process.env.MONGO_DB_PASSWORD}@${process.env.MONGODB_CLUSTER}/${dbName}?retryWrites=true&w=majority`;
    
    console.log(`Connecting to MongoDB (${runMode} mode)...`);
    await mongoose.connect(mongoUri);
    console.log('Connected to MongoDB');
    
    const gameSchema = new mongoose.Schema({}, { strict: false });
    const Game = mongoose.model('Game', gameSchema);
    
    // Check goalkeepers with clean sheets
    const gkWithCleanSheets = await Game.aggregate([
      { $match: { league: new mongoose.Types.ObjectId('65ecb1eb2f272e434483a821') } },
      {
        $project: {
          players: {
            $concatArrays: [
              { $ifNull: ["$homeTeamPlayersPerformance", []] },
              { $ifNull: ["$awayTeamPlayersPerformance", []] }
            ]
          }
        }
      },
      { $unwind: "$players" },
      { $match: { "players.cleanSheet": true } },
      {
        $group: {
          _id: "$players.playerId",
          cleanSheets: { $sum: 1 },
          totalGames: { $sum: 1 }
        }
      },
      {
        $lookup: {
          from: "players",
          localField: "_id",
          foreignField: "_id",
          as: "player"
        }
      },
      { $unwind: "$player" },
      { $match: { "player.position": "GK" } }, // Only goalkeepers
      {
        $project: {
          playerName: "$player.name",
          position: "$player.position",
          cleanSheets: 1,
          totalGames: 1,
          cleanSheetsPerGame: { $divide: ["$cleanSheets", "$totalGames"] }
        }
      },
      { $sort: { cleanSheets: -1 } }
    ]);
    
    console.log(`\nGoalkeepers with clean sheets (${gkWithCleanSheets.length} found):`);
    if (gkWithCleanSheets.length === 0) {
      console.log('No goalkeepers found with clean sheets!');
    } else {
      gkWithCleanSheets.forEach(player => {
        console.log(`${player.playerName}: ${player.cleanSheets} clean sheets in ${player.totalGames} games (${(player.cleanSheetsPerGame * 100).toFixed(1)}%)`);
      });
    }
    
    // Also check total number of goalkeepers in the league
    const playerSchema = new mongoose.Schema({}, { strict: false });
    const Player = mongoose.model('Player', playerSchema);
    
    const totalGKs = await Player.countDocuments({ position: "GK" });
    console.log(`\nTotal goalkeepers in database: ${totalGKs}`);
    
    // Get some sample GK names
    const sampleGKs = await Player.find({ position: "GK" }).limit(5).select('name position');
    console.log('Sample goalkeepers:');
    sampleGKs.forEach(gk => {
      console.log(`- ${gk.name} (${gk.position})`);
    });
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    mongoose.connection.close();
  }
}

checkGKCleanSheets();
