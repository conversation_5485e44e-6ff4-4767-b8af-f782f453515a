import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { LeagueService } from '../../services/league.service';
import { NotificationService } from '../../services/notification.service';
import { ILeague } from '../../shared/models/league-table.model';
import { TopScorer } from '../../shared/models/topscorer.model';
import { TopAssister } from '../../shared/models/topassister.model';
import { Router } from '@angular/router';
import { TabItem } from '../shared/scrollable-tabs/scrollable-tabs.component';
import { AllTimeAvgRatingByPositionComponent } from '../all-time-avg-rating-by-position/all-time-avg-rating-by-position.component';
import { MostHattricksComponent } from '../most-hattricks/most-hattricks.component';
import { MostCleanSheetsComponent } from '../most-clean-sheets/most-clean-sheets.component';
import { MostWinningPercentageTeamsComponent } from '../most-winning-percentage-teams/most-winning-percentage-teams.component';
import { MostWinningPercentagePlayersComponent } from '../most-winning-percentage-players/most-winning-percentage-players.component';

@Component({
  selector: 'app-all-time-stats',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    AllTimeAvgRatingByPositionComponent,
    MostHattricksComponent,
    MostCleanSheetsComponent,
    MostWinningPercentageTeamsComponent,
    MostWinningPercentagePlayersComponent
  ],
  templateUrl: './all-time-stats.component.html',
  styleUrls: ['./all-time-stats.component.scss']
})
export class AllTimeStatsComponent implements OnInit {
  leagues: ILeague[] = [];
  selectedLeagueId: string = '';
  allTimeTopScorers: TopScorer[] = [];
  allTimeTopAssisters: TopAssister[] = [];
  isLoadingLeagues = false;
  isLoadingStats = false;
  activeTab: 'scorers' | 'assisters' | 'avgRating' | 'hattricks' | 'cleanSheets' | 'winningTeams' | 'winningPlayers' = 'scorers';

  get statsTabs(): TabItem[] {
    return [
      {
        id: 'scorers',
        label: 'Top Scorers',
        icon: 'fas fa-futbol',
        count: this.allTimeTopScorers.length
      },
      {
        id: 'assisters',
        label: 'Top Assisters',
        icon: 'fas fa-hands-helping',
        count: this.allTimeTopAssisters.length
      },
      {
        id: 'avgRating',
        label: 'Best Avg Rating',
        icon: 'fas fa-star',
        count: 0
      },
      {
        id: 'hattricks',
        label: 'Most Hattricks',
        icon: 'fas fa-fire',
        count: 0
      },
      {
        id: 'cleanSheets',
        label: 'Most Clean Sheets',
        icon: 'fas fa-shield-alt',
        count: 0
      },
      {
        id: 'winningTeams',
        label: 'Winning Teams',
        icon: 'fas fa-trophy',
        count: 0
      },
      {
        id: 'winningPlayers',
        label: 'Winning Players',
        icon: 'fas fa-star',
        count: 0
      }
    ];
  }

  constructor(
    private leagueService: LeagueService,
    private notificationService: NotificationService,
    private router: Router
  ) {}

  async ngOnInit(): Promise<void> {
    await this.loadLeagues();
  }

  private async loadLeagues(): Promise<void> {
    this.isLoadingLeagues = true;
    try {
      this.leagues = await this.leagueService.getAllLeagues();
      
      // Auto-select the first league if available
      if (this.leagues.length > 0) {
        this.selectedLeagueId = this.leagues[0].id;
        await this.loadAllTimeStats();
      }
    } catch (error) {
      console.error('Error loading leagues:', error);
      this.notificationService.error('Failed to load leagues');
    } finally {
      this.isLoadingLeagues = false;
    }
  }

  async onLeagueChange(): Promise<void> {
    if (this.selectedLeagueId) {
      await this.loadAllTimeStats();
    } else {
      this.allTimeTopScorers = [];
      this.allTimeTopAssisters = [];
    }
  }

  private async loadAllTimeStats(): Promise<void> {
    if (!this.selectedLeagueId) return;

    this.isLoadingStats = true;
    try {
      const [topScorers, topAssisters] = await Promise.all([
        this.leagueService.getAllTimeTopScorers(this.selectedLeagueId, 50),
        this.leagueService.getAllTimeTopAssisters(this.selectedLeagueId, 50)
      ]);

      this.allTimeTopScorers = topScorers;
      this.allTimeTopAssisters = topAssisters;
    } catch (error) {
      console.error('Error loading all-time stats:', error);
      this.notificationService.error('Failed to load all-time statistics');
    } finally {
      this.isLoadingStats = false;
    }
  }

  setActiveTab(tab: 'scorers' | 'assisters' | 'avgRating' | 'hattricks' | 'cleanSheets' | 'winningTeams' | 'winningPlayers'): void;
  setActiveTab(tab: TabItem): void;
  setActiveTab(tab: 'scorers' | 'assisters' | 'avgRating' | 'hattricks' | 'cleanSheets' | 'winningTeams' | 'winningPlayers' | TabItem): void {
    if (typeof tab === 'string') {
      this.activeTab = tab;
    } else {
      this.activeTab = tab.id as 'scorers' | 'assisters' | 'avgRating' | 'hattricks' | 'cleanSheets' | 'winningTeams' | 'winningPlayers';
    }
  }

  onStatTypeChange(): void {
    // Method called when dropdown selection changes
    // The activeTab is already updated via ngModel
  }

  navigateToPlayer(playerId: string): void {
    this.router.navigate(['/player', playerId]);
  }

  navigateToTeam(teamId: string): void {
    this.router.navigate(['/team-details', teamId]);
  }

  get selectedLeague(): ILeague | undefined {
    return this.leagues.find(league => league.id === this.selectedLeagueId);
  }

  getPlayerRank(index: number): string {
    const rank = index + 1;
    if (rank === 1) return '🥇';
    if (rank === 2) return '🥈';
    if (rank === 3) return '🥉';
    return `${rank}`;
  }

  getStatValue(player: TopScorer | TopAssister): number {
    if ('goals' in player) {
      return player.goals;
    } else {
      return player.assists;
    }
  }

  getStatLabel(): string {
    return this.activeTab === 'scorers' ? 'Goals' : 'Assists';
  }

  getPerGameStat(player: TopScorer | TopAssister): number {
    if ('goalsPerGame' in player) {
      return player.goalsPerGame;
    } else {
      return player.assistsPerGame;
    }
  }

  onImageError(event: Event): void {
    const img = event.target as HTMLImageElement;
    if (img) {
      img.src = 'assets/Icons/User.jpg';
    }
  }
}
