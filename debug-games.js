const mongoose = require('mongoose');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/proclubsstats');

const gameSchema = new mongoose.Schema({}, { strict: false });
const Game = mongoose.model('Game', gameSchema);

async function debugGames() {
  try {
    console.log('=== DEBUGGING GAMES ===');
    
    // Check total games
    const totalGames = await Game.countDocuments();
    console.log(`Total games: ${totalGames}`);
    
    // Check games with results
    const gamesWithResults = await Game.countDocuments({ result: { $exists: true, $ne: null } });
    console.log(`Games with results: ${gamesWithResults}`);
    
    // Check games by status
    const statusCounts = await Game.aggregate([
      { $group: { _id: "$status", count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);
    console.log('Games by status:', statusCounts);
    
    // Get a sample game with result
    const sampleGameWithResult = await Game.findOne({ 
      result: { $exists: true, $ne: null },
      status: { $in: ["PLAYED", "COMPLETED"] }
    });
    
    if (sampleGameWithResult) {
      console.log('\n=== SAMPLE GAME WITH RESULT ===');
      console.log('ID:', sampleGameWithResult._id);
      console.log('Status:', sampleGameWithResult.status);
      console.log('Result:', JSON.stringify(sampleGameWithResult.result, null, 2));
      console.log('Home Team:', sampleGameWithResult.homeTeam);
      console.log('Away Team:', sampleGameWithResult.awayTeam);
    } else {
      console.log('No games found with results and PLAYED/COMPLETED status');
    }
    
    // Check a few games with different result patterns
    const drawGames = await Game.countDocuments({ 
      'result.homeTeamGoals': { $exists: true },
      'result.awayTeamGoals': { $exists: true },
      $expr: { $eq: ['$result.homeTeamGoals', '$result.awayTeamGoals'] }
    });
    
    const homeWins = await Game.countDocuments({ 
      'result.homeTeamGoals': { $exists: true },
      'result.awayTeamGoals': { $exists: true },
      $expr: { $gt: ['$result.homeTeamGoals', '$result.awayTeamGoals'] }
    });
    
    const awayWins = await Game.countDocuments({ 
      'result.homeTeamGoals': { $exists: true },
      'result.awayTeamGoals': { $exists: true },
      $expr: { $gt: ['$result.awayTeamGoals', '$result.homeTeamGoals'] }
    });
    
    console.log('\n=== GAME RESULTS BREAKDOWN ===');
    console.log(`Draw games: ${drawGames}`);
    console.log(`Home wins: ${homeWins}`);
    console.log(`Away wins: ${awayWins}`);
    
    // Get some sample results
    const sampleResults = await Game.find({ 
      result: { $exists: true, $ne: null } 
    }).limit(5).select('result status homeTeam awayTeam');
    
    console.log('\n=== SAMPLE RESULTS ===');
    sampleResults.forEach((game, index) => {
      console.log(`Game ${index + 1}:`);
      console.log(`  Status: ${game.status}`);
      console.log(`  Result: ${JSON.stringify(game.result)}`);
      console.log(`  Home: ${game.homeTeam}, Away: ${game.awayTeam}`);
    });
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    mongoose.connection.close();
  }
}

debugGames();
