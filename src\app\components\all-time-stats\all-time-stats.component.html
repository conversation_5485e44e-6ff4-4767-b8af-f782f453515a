<div class="all-time-stats-container">
  <!-- <PERSON>er -->
  <div class="page-header">
    <h1 class="page-title">
      <i class="fas fa-trophy"></i>
      All-Time Statistics
    </h1>
    <p class="page-description">
      Historical top performers across all seasons
    </p>
  </div>

  <!-- League Selection -->
  <div class="league-selection" *ngIf="!isLoadingLeagues">
    <div class="form-group">
      <label for="leagueSelect" class="form-label">
        <i class="fas fa-futbol"></i>
        Select League
      </label>
      <select 
        id="leagueSelect"
        class="form-select"
        [(ngModel)]="selectedLeagueId"
        (change)="onLeagueChange()">
        <option value="">Choose a league...</option>
        <option *ngFor="let league of leagues" [value]="league.id">
          {{ league.name }}
        </option>
      </select>
    </div>
  </div>

  <!-- Loading States -->
  <div class="loading-container" *ngIf="isLoadingLeagues">
    <i class="fas fa-spinner fa-spin"></i>
    <p>Loading leagues...</p>
  </div>

  <!-- Stats Content -->
  <div class="stats-content" *ngIf="selectedLeagueId && !isLoadingLeagues">
    <!-- League Info -->
    <div class="league-info">
      <h2 class="league-title">{{ selectedLeague?.name }}</h2>
      <p class="league-subtitle">All-Time Records</p>
    </div>

    <!-- Stat Type Selection -->
    <div class="stat-selection">
      <div class="form-group">
        <label for="statTypeSelect" class="form-label">
          <i class="fas fa-chart-bar"></i>
          Select Statistic Type
        </label>
        <select
          id="statTypeSelect"
          class="form-select"
          [(ngModel)]="activeTab"
          (change)="onStatTypeChange()">
          <option *ngFor="let tab of statsTabs" [value]="tab.id">
            <i [class]="tab.icon"></i> {{ tab.label }}
          </option>
        </select>
      </div>
    </div>

    <!-- Loading Stats -->
    <div class="loading-container" *ngIf="isLoadingStats">
      <i class="fas fa-spinner fa-spin"></i>
      <p>Loading statistics...</p>
    </div>

    <!-- Stats Tables -->
    <div class="stats-tables" *ngIf="!isLoadingStats">
      <!-- Top Scorers -->
      <div class="stats-table" *ngIf="activeTab === 'scorers'">
        <div class="table-header">
          <h3 class="table-title">
            <i class="fas fa-crown"></i>
            All-Time Top Scorers
          </h3>
          <p class="table-subtitle">Greatest goal scorers in league history</p>
        </div>

        <div class="players-list" *ngIf="allTimeTopScorers.length > 0; else noScorersTemplate">
          <div class="player-row"
               *ngFor="let scorer of allTimeTopScorers; let i = index"
               (click)="navigateToPlayer(scorer.playerId)"
               [class.first-place]="i === 0"
               [class.second-place]="i === 1"
               [class.third-place]="i === 2">
            
            <div class="rank-section">
              <span class="rank">{{ getPlayerRank(i) }}</span>
              <i class="fas fa-crown crown-icon" *ngIf="i === 0"></i>
            </div>

            <div class="player-info">
              <img [src]="scorer.playerImgUrl || 'assets/Icons/User.jpg'"
                   [alt]="scorer.playerName"
                   class="player-avatar"
                   (error)="onImageError($event)">
              
              <div class="player-details">
                <h4 class="player-name">{{ scorer.playerName }}</h4>
                <p class="player-position">{{ scorer.position }}</p>
              </div>
            </div>

            <div class="team-info" (click)="navigateToTeam(scorer.teamId); $event.stopPropagation()">
              <span class="team-name">{{ scorer.teamName }}</span>
            </div>

            <div class="stats-section">
              <div class="primary-stat">
                <span class="stat-value">{{ scorer.goals }}</span>
                <span class="stat-label">Goals</span>
              </div>
              <div class="secondary-stats">
                <div class="stat-item">
                  <span class="stat-value">{{ scorer.games }}</span>
                  <span class="stat-label">Games</span>
                </div>
                <div class="stat-item">
                  <span class="stat-value">{{ scorer.goalsPerGame.toFixed(2) }}</span>
                  <span class="stat-label">Per Game</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <ng-template #noScorersTemplate>
          <div class="no-data">
            <i class="fas fa-futbol"></i>
            <p>No scoring data available for this league</p>
          </div>
        </ng-template>
      </div>

      <!-- Top Assisters -->
      <div class="stats-table" *ngIf="activeTab === 'assisters'">
        <div class="table-header">
          <h3 class="table-title">
            <i class="fas fa-crown"></i>
            All-Time Top Assisters
          </h3>
          <p class="table-subtitle">Greatest playmakers in league history</p>
        </div>

        <div class="players-list" *ngIf="allTimeTopAssisters.length > 0; else noAssistersTemplate">
          <div class="player-row"
               *ngFor="let assister of allTimeTopAssisters; let i = index"
               (click)="navigateToPlayer(assister.playerId)"
               [class.first-place]="i === 0"
               [class.second-place]="i === 1"
               [class.third-place]="i === 2">
            
            <div class="rank-section">
              <span class="rank">{{ getPlayerRank(i) }}</span>
              <i class="fas fa-crown crown-icon" *ngIf="i === 0"></i>
            </div>

            <div class="player-info">
              <img [src]="assister.playerImgUrl || 'assets/Icons/User.jpg'"
                   [alt]="assister.playerName"
                   class="player-avatar"
                   (error)="onImageError($event)">
              
              <div class="player-details">
                <h4 class="player-name">{{ assister.playerName }}</h4>
                <p class="player-position">{{ assister.position }}</p>
              </div>
            </div>

            <div class="team-info" (click)="navigateToTeam(assister.teamId); $event.stopPropagation()">
              <span class="team-name">{{ assister.teamName }}</span>
            </div>

            <div class="stats-section">
              <div class="primary-stat">
                <span class="stat-value">{{ assister.assists }}</span>
                <span class="stat-label">Assists</span>
              </div>
              <div class="secondary-stats">
                <div class="stat-item">
                  <span class="stat-value">{{ assister.games }}</span>
                  <span class="stat-label">Games</span>
                </div>
                <div class="stat-item">
                  <span class="stat-value">{{ assister.assistsPerGame.toFixed(2) }}</span>
                  <span class="stat-label">Per Game</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <ng-template #noAssistersTemplate>
          <div class="no-data">
            <i class="fas fa-hands-helping"></i>
            <p>No assist data available for this league</p>
          </div>
        </ng-template>
      </div>

      <!-- All-Time Avg Rating by Position -->
      <div class="stats-table" *ngIf="activeTab === 'avgRating'">
        <app-all-time-avg-rating-by-position
          [hideTitle]="true"
          [leagueId]="selectedLeagueId">
        </app-all-time-avg-rating-by-position>
      </div>

      <!-- Most Hattricks -->
      <div class="stats-table" *ngIf="activeTab === 'hattricks'">
        <app-most-hattricks
          [hideTitle]="true"
          [leagueId]="selectedLeagueId">
        </app-most-hattricks>
      </div>

      <!-- Most Clean Sheets -->
      <div class="stats-table" *ngIf="activeTab === 'cleanSheets'">
        <app-most-clean-sheets
          [hideTitle]="true"
          [leagueId]="selectedLeagueId">
        </app-most-clean-sheets>
      </div>

      <!-- Most Winning Percentage Teams -->
      <div class="stats-table" *ngIf="activeTab === 'winningTeams'">
        <app-most-winning-percentage-teams
          [hideTitle]="true"
          [leagueId]="selectedLeagueId">
        </app-most-winning-percentage-teams>
      </div>

      <!-- Most Winning Percentage Players -->
      <div class="stats-table" *ngIf="activeTab === 'winningPlayers'">
        <app-most-winning-percentage-players
          [hideTitle]="true"
          [leagueId]="selectedLeagueId">
        </app-most-winning-percentage-players>
      </div>
    </div>
  </div>
</div>
