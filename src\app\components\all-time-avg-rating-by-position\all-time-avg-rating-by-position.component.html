<div class="all-time-avg-rating-container theme-transition">
  <!-- Header -->
  <div class="header-section" *ngIf="!hideTitle">
    <div class="header-content">
      <h1 class="header-title">
        <i class="fas fa-star"></i>
        All-Time Best Average Rating
      </h1>
      <p class="header-subtitle">Top performers by selected position across all seasons</p>
    </div>
  </div>

  <!-- League Selection -->
  <div class="controls-section" *ngIf="!leagueId">
    <div class="control-group">
      <label for="league-select" class="control-label">
        <i class="fas fa-trophy"></i>
        League
      </label>
      <select 
        id="league-select"
        class="control-select"
        [(ngModel)]="selectedLeagueId"
        (change)="onLeagueChange()">
        <option value="">Select a league</option>
        <option *ngFor="let league of leagues" [value]="league.id">
          {{ league.name }}
        </option>
      </select>
    </div>
  </div>

  <!-- Filters -->
  <div class="filters-section" *ngIf="selectedLeagueId">
    <div class="filter-group">
      <label for="position-select" class="filter-label">
        <i class="fas fa-map-marker-alt"></i>
        Position
      </label>
      <select 
        id="position-select"
        class="filter-select"
        [(ngModel)]="chosenPosition"
        (change)="onPositionChange()">
        <option *ngFor="let option of playablePositionOptions" [value]="option.value">
          {{ option.displayText }}
        </option>
      </select>
    </div>

    <div class="filter-group">
      <label for="minimum-games" class="filter-label">
        <i class="fas fa-gamepad"></i>
        Minimum Games
      </label>
      <input 
        id="minimum-games"
        type="number"
        class="filter-input"
        [(ngModel)]="minimumGames"
        (change)="onMinimumGamesChange()"
        min="1"
        max="100">
    </div>
  </div>

  <!-- Top Players by Position Grid -->
  <div class="top-players-grid" *ngIf="selectedLeagueId && !isLoading">
    <div class="grid-header">
      <h3 class="grid-title">
        <i class="fas fa-crown"></i>
        Best by Position
      </h3>
    </div>
    
    <div class="position-cards">
      <div 
        class="position-card"
        *ngFor="let position of keyPositions"
        [class.has-player]="topPlayersByPosition[position]"
        (click)="topPlayersByPosition[position] && onPlayerClick(topPlayersByPosition[position])">
        
        <div class="position-header">
          <span class="position-name">{{ getPositionDisplayName(position) }}</span>
        </div>
        
        <div class="player-content" *ngIf="topPlayersByPosition[position]; else noPlayerTemplate">
          <img 
            [src]="topPlayersByPosition[position].playerImgUrl || 'assets/Icons/User.jpg'"
            [alt]="topPlayersByPosition[position].playerName"
            class="player-image"
            (error)="onImageError($event)">
          
          <div class="player-info">
            <h4 class="player-name">{{ topPlayersByPosition[position].playerName }}</h4>
            <p class="team-name">{{ topPlayersByPosition[position].teamName }}</p>
            <div class="rating-badge">
              {{ topPlayersByPosition[position].avgRating }}
            </div>
          </div>
        </div>
        
        <ng-template #noPlayerTemplate>
          <div class="no-player">
            <i class="fas fa-user-slash"></i>
            <span>No data</span>
          </div>
        </ng-template>
      </div>
    </div>
  </div>

  <!-- Detailed Rankings -->
  <div class="rankings-section" *ngIf="selectedLeagueId && !isLoading">
    <div class="rankings-header">
      <h3 class="rankings-title">
        <i class="fas fa-list-ol"></i>
        <span *ngIf="isFilteringByPosition()">{{ getPositionDisplayName(chosenPosition) }} Rankings</span>
        <span *ngIf="!isFilteringByPosition()">Complete Rankings</span>
      </h3>
      <p class="rankings-subtitle">
        Minimum {{ minimumGames }} games played
      </p>
    </div>

    <div class="players-list" *ngIf="allTimeTopAvgRatingData.length > 0; else noDataTemplate">
      <div 
        class="player-row"
        *ngFor="let player of allTimeTopAvgRatingData; let i = index"
        (click)="onPlayerClick(player)"
        [class.first-place]="i === 0"
        [class.second-place]="i === 1"
        [class.third-place]="i === 2">
        
        <div class="rank-section">
          <span class="rank">{{ getPlayerRank(i) }}</span>
          <i class="fas fa-crown crown-icon" *ngIf="i === 0"></i>
        </div>

        <div class="player-info">
          <img 
            [src]="player.playerImgUrl || 'assets/Icons/User.jpg'"
            [alt]="player.playerName"
            class="player-avatar"
            (error)="onImageError($event)">
          
          <div class="player-details">
            <h4 class="player-name">{{ player.playerName }}</h4>
            <p class="player-position">{{ player.position }}</p>
          </div>
        </div>

        <div class="team-info" (click)="onTeamClick(player.teamId); $event.stopPropagation()">
          <span class="team-name">{{ player.teamName }}</span>
        </div>

        <div class="stats-section">
          <div class="primary-stat">
            <span class="stat-value">{{ player.avgRating }}</span>
            <span class="stat-label">Rating</span>
          </div>
          <div class="secondary-stats">
            <div class="stat-item">
              <span class="stat-value">{{ player.games }}</span>
              <span class="stat-label">Games</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ player.goals }}</span>
              <span class="stat-label">Goals</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ player.assists }}</span>
              <span class="stat-label">Assists</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <ng-template #noDataTemplate>
      <div class="no-data">
        <i class="fas fa-chart-line"></i>
        <h3>No Data Available</h3>
        <p>No players found with the current filters</p>
      </div>
    </ng-template>
  </div>

  <!-- Loading State -->
  <div class="loading-container" *ngIf="isLoading">
    <div class="spinner">
      <i class="fas fa-spinner fa-spin"></i>
      <p>Loading statistics...</p>
    </div>
  </div>
</div>
