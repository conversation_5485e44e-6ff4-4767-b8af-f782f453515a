const redis = require('redis');
require('dotenv').config();

async function clearCache() {
  const client = redis.createClient({
    password: process.env.REDIS_PASSWORD,
    socket: {
      host: process.env.REDIS_HOST,
      port: 19402,
    },
  });
  
  try {
    await client.connect();
    console.log('Connected to Redis');
    
    // Clear the specific cache keys
    const keys = [
      'mostWinningPercentageTeams:65ecb1eb2f272e434483a821:10',
      'mostWinningPercentagePlayers:65ecb1eb2f272e434483a821:10',
      'mostCleanSheets:65ecb1eb2f272e434483a821'
    ];
    
    for (const key of keys) {
      const result = await client.del(key);
      console.log(`Deleted ${key}: ${result ? 'success' : 'not found'}`);
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await client.quit();
  }
}

clearCache();
