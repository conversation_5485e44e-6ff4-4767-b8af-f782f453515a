const mongoose = require('mongoose');
require('dotenv').config();

async function checkPositions() {
  try {
    const runMode = process.env.RUN_MODE || 'prod';
    const dbName = runMode === 'prod' ? process.env.MONGODB_DB_PROD : process.env.MONGODB_DB_DEV;
    const mongoUri = `mongodb+srv://${process.env.MONGO_DB_USERNAME}:${process.env.MONGO_DB_PASSWORD}@${process.env.MONGODB_CLUSTER}/${dbName}?retryWrites=true&w=majority`;
    
    console.log(`Connecting to MongoDB (${runMode} mode)...`);
    await mongoose.connect(mongoUri);
    console.log('Connected to MongoDB');
    
    const playerSchema = new mongoose.Schema({}, { strict: false });
    const Player = mongoose.model('Player', playerSchema);
    
    // Get all unique positions
    const positions = await Player.distinct('position');
    console.log('All positions in database:', positions);
    
    // Look for goalkeeper-like positions
    const gkPositions = positions.filter(pos => 
      pos && pos.toLowerCase().includes('gk') || 
      pos && pos.toLowerCase().includes('goal') ||
      pos && pos.toLowerCase().includes('keeper')
    );
    console.log('Goalkeeper-like positions:', gkPositions);
    
    // Get some sample players with clean sheets
    const gameSchema = new mongoose.Schema({}, { strict: false });
    const Game = mongoose.model('Game', gameSchema);
    
    const playersWithCleanSheets = await Game.aggregate([
      { $match: { league: new mongoose.Types.ObjectId('65ecb1eb2f272e434483a821') } },
      {
        $project: {
          players: {
            $concatArrays: [
              { $ifNull: ["$homeTeamPlayersPerformance", []] },
              { $ifNull: ["$awayTeamPlayersPerformance", []] }
            ]
          }
        }
      },
      { $unwind: "$players" },
      { $match: { "players.cleanSheet": true } },
      {
        $group: {
          _id: "$players.playerId",
          cleanSheets: { $sum: 1 }
        }
      },
      { $match: { cleanSheets: { $gte: 5 } } }, // Players with at least 5 clean sheets
      {
        $lookup: {
          from: "players",
          localField: "_id",
          foreignField: "_id",
          as: "player"
        }
      },
      { $unwind: "$player" },
      {
        $project: {
          playerName: "$player.name",
          position: "$player.position",
          cleanSheets: 1
        }
      },
      { $sort: { cleanSheets: -1 } },
      { $limit: 10 }
    ]);
    
    console.log('\nPlayers with most clean sheets and their positions:');
    playersWithCleanSheets.forEach(player => {
      console.log(`${player.playerName}: ${player.position} (${player.cleanSheets} clean sheets)`);
    });
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    mongoose.connection.close();
  }
}

checkPositions();
