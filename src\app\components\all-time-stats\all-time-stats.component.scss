/* === ALL-TIME STATS COMPONENT === */

.all-time-stats-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-lg);
  background: var(--surface-primary);
  min-height: 100vh;
}

/* === PAGE HEADER === */
.page-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-xl);
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  border-radius: var(--radius-xl);
  color: white;
  box-shadow: var(--shadow-lg);

  .page-title {
    font-size: var(--text-3xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);

    i {
      font-size: var(--text-2xl);
    }
  }

  .page-description {
    font-size: var(--text-lg);
    opacity: 0.9;
    margin: 0;
  }
}

/* === LEAGUE SELECTION === */
.league-selection {
  margin-bottom: var(--spacing-xl);
  
  .form-group {
    max-width: 400px;
    margin: 0 auto;

    .form-label {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      font-weight: var(--font-weight-semibold);
      color: var(--text-primary);
      margin-bottom: var(--spacing-sm);
      font-size: var(--text-base);
    }

    .form-select {
      width: 100%;
      padding: var(--spacing-md);
      border: 2px solid var(--border-primary);
      border-radius: var(--radius-lg);
      background: var(--surface-secondary);
      color: var(--text-primary);
      font-size: var(--text-base);
      transition: all 0.2s ease;

      &:focus {
        outline: none;
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px rgba(var(--primary-500-rgb), 0.1);
      }
    }
  }
}

/* === STAT SELECTION === */
.stat-selection {
  margin-bottom: 30px;
  padding: 20px;
  background: var(--surface-primary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-primary);

  .form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-width: 400px;

    .form-label {
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 8px;
      color: var(--text-primary);

      i {
        color: var(--primary-500);
      }
    }

    .form-select {
      padding: 12px 16px;
      border: 2px solid var(--border-primary);
      border-radius: var(--radius-lg);
      background: var(--surface-secondary);
      color: var(--text-primary);
      font-size: var(--text-base);
      transition: all 0.3s ease;
      cursor: pointer;

      &:focus {
        outline: none;
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px rgba(var(--primary-500-rgb), 0.1);
      }

      &:hover {
        border-color: var(--primary-500);
      }

      option {
        background: var(--surface-secondary);
        color: var(--text-primary);
        padding: 8px;
      }
    }
  }
}

/* === LEAGUE INFO === */
.league-info {
  text-align: center;
  margin-bottom: var(--spacing-xl);

  .league-title {
    font-size: var(--text-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
  }

  .league-subtitle {
    font-size: var(--text-lg);
    color: var(--text-secondary); // Use secondary for subtitles
    margin: 0;
  }
}

/* === TAB NAVIGATION === */
.tab-navigation {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
  
  .tab-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--surface-secondary);
    border: 2px solid var(--border-primary);
    border-radius: var(--radius-lg);
    color: var(--text-secondary);
    font-weight: var(--font-weight-semibold);
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: var(--surface-tertiary);
      border-color: var(--primary-300);
      color: var(--text-primary);
    }

    &.active {
      background: var(--primary-500);
      border-color: var(--primary-500);
      color: white;
      box-shadow: var(--shadow-md);
    }

    .tab-count {
      background: rgba(255, 255, 255, 0.2);
      padding: 2px 8px;
      border-radius: var(--radius-full);
      font-size: var(--text-sm);
    }
  }

  @media (max-width: 768px) {
    flex-direction: column;
    
    .tab-btn {
      justify-content: center;
    }
  }
}

/* === STATS TABLES === */
.stats-table {
  background: var(--surface-secondary);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-primary);

  .table-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);

    .table-title {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: var(--spacing-sm);
      font-size: var(--text-xl);
      font-weight: var(--font-weight-bold);
      color: var(--text-primary);
      margin-bottom: var(--spacing-sm);
    }

    .table-subtitle {
      color: var(--text-secondary); // Use secondary for subtitles to maintain hierarchy
      margin: 0;
    }
  }
}

/* === PLAYERS LIST === */
.players-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.player-row {
  display: grid;
  grid-template-columns: auto 1fr auto auto;
  gap: var(--spacing-lg);
  align-items: center;
  padding: var(--spacing-lg);
  background: var(--surface-primary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-primary);
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-300);
  }

  // === TOP 3 PODIUM STYLING ===
  &.first-place {
    border: 3px solid #fbbf24 !important;
    background: linear-gradient(135deg, rgba(251, 191, 36, 0.3), rgba(251, 191, 36, 0.1)) !important;
    box-shadow: 0 8px 25px rgba(251, 191, 36, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #fbbf24, #f59e0b, #fbbf24);
      animation: shimmer 2s ease-in-out infinite;
    }

    &:hover {
      background: linear-gradient(135deg, rgba(251, 191, 36, 0.4), rgba(251, 191, 36, 0.2)) !important;
      transform: translateY(-4px) scale(1.02);
      box-shadow: 0 12px 35px rgba(251, 191, 36, 0.5), inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }

    .rank {
      color: #1f2937 !important;
      background: linear-gradient(135deg, #fbbf24, #f59e0b) !important;
      font-weight: var(--font-weight-bold);
      font-size: var(--text-2xl) !important;
      text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
      box-shadow: 0 4px 12px rgba(251, 191, 36, 0.5), inset 0 1px 0 rgba(255, 255, 255, 0.3);
      border: 2px solid #f59e0b !important;
    }

    .player-name {
      color: var(--text-primary) !important;
      font-weight: var(--font-weight-bold);
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    .player-position {
      color: var(--text-secondary) !important;
      font-weight: var(--font-weight-medium);
    }

    .team-name {
      color: var(--text-primary) !important;
      font-weight: var(--font-weight-bold);
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    .primary-stat .stat-value {
      color: #fbbf24 !important;
      font-weight: var(--font-weight-bold);
      font-size: var(--text-3xl) !important;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      filter: drop-shadow(0 0 8px rgba(251, 191, 36, 0.6));
    }

    .stat-value {
      color: var(--text-primary) !important;
      font-weight: var(--font-weight-semibold);
    }

    .stat-label {
      color: var(--text-secondary) !important;
      font-weight: var(--font-weight-medium);
    }

    .player-avatar {
      border: 3px solid #fbbf24 !important;
      box-shadow: 0 4px 12px rgba(251, 191, 36, 0.5);
    }

    .team-info {
      background: rgba(251, 191, 36, 0.2) !important;
      border: 2px solid rgba(251, 191, 36, 0.5) !important;
      color: var(--text-primary) !important;
      font-weight: var(--font-weight-bold);

      &:hover {
        background: rgba(251, 191, 36, 0.3) !important;
        border-color: #fbbf24 !important;
        transform: scale(1.05);
      }
    }
  }

  &.second-place {
    border: 3px solid #60a5fa !important;
    background: linear-gradient(135deg, rgba(96, 165, 250, 0.3), rgba(96, 165, 250, 0.1)) !important;
    box-shadow: 0 8px 25px rgba(96, 165, 250, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #60a5fa, #3b82f6, #60a5fa);
      animation: shimmer 2s ease-in-out infinite;
    }

    &:hover {
      background: linear-gradient(135deg, rgba(96, 165, 250, 0.4), rgba(96, 165, 250, 0.2)) !important;
      transform: translateY(-4px) scale(1.02);
      box-shadow: 0 12px 35px rgba(96, 165, 250, 0.5), inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }

    .rank {
      color: #1f2937 !important;
      background: linear-gradient(135deg, #60a5fa, #3b82f6) !important;
      font-weight: var(--font-weight-bold);
      font-size: var(--text-2xl) !important;
      text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
      box-shadow: 0 4px 12px rgba(96, 165, 250, 0.5), inset 0 1px 0 rgba(255, 255, 255, 0.3);
      border: 2px solid #3b82f6 !important;
    }

    .player-name {
      color: var(--text-primary) !important;
      font-weight: var(--font-weight-bold);
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    .player-position {
      color: var(--text-secondary) !important;
      font-weight: var(--font-weight-medium);
    }

    .team-name {
      color: var(--text-primary) !important;
      font-weight: var(--font-weight-bold);
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    .primary-stat .stat-value {
      color: #60a5fa !important;
      font-weight: var(--font-weight-bold);
      font-size: var(--text-3xl) !important;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      filter: drop-shadow(0 0 8px rgba(96, 165, 250, 0.6));
    }

    .stat-value {
      color: var(--text-primary) !important;
      font-weight: var(--font-weight-semibold);
    }

    .stat-label {
      color: var(--text-secondary) !important;
      font-weight: var(--font-weight-medium);
    }

    .player-avatar {
      border: 3px solid #60a5fa !important;
      box-shadow: 0 4px 12px rgba(96, 165, 250, 0.5);
    }

    .team-info {
      background: rgba(96, 165, 250, 0.2) !important;
      border: 2px solid rgba(96, 165, 250, 0.5) !important;
      color: var(--text-primary) !important;
      font-weight: var(--font-weight-bold);

      &:hover {
        background: rgba(96, 165, 250, 0.3) !important;
        border-color: #60a5fa !important;
        transform: scale(1.05);
      }
    }
  }

  &.third-place {
    border: 3px solid #ea580c !important;
    background: linear-gradient(135deg, rgba(234, 88, 12, 0.3), rgba(234, 88, 12, 0.1)) !important;
    box-shadow: 0 8px 25px rgba(234, 88, 12, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #ea580c, #dc2626, #ea580c);
      animation: shimmer 2s ease-in-out infinite;
    }

    &:hover {
      background: linear-gradient(135deg, rgba(234, 88, 12, 0.4), rgba(234, 88, 12, 0.2)) !important;
      transform: translateY(-4px) scale(1.02);
      box-shadow: 0 12px 35px rgba(234, 88, 12, 0.5), inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }

    .rank {
      color: #1f2937 !important;
      background: linear-gradient(135deg, #ea580c, #dc2626) !important;
      font-weight: var(--font-weight-bold);
      font-size: var(--text-2xl) !important;
      text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
      box-shadow: 0 4px 12px rgba(234, 88, 12, 0.5), inset 0 1px 0 rgba(255, 255, 255, 0.3);
      border: 2px solid #dc2626 !important;
    }

    .player-name {
      color: var(--text-primary) !important;
      font-weight: var(--font-weight-bold);
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    .player-position {
      color: var(--text-secondary) !important;
      font-weight: var(--font-weight-medium);
    }

    .team-name {
      color: var(--text-primary) !important;
      font-weight: var(--font-weight-bold);
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    .primary-stat .stat-value {
      color: #ea580c !important;
      font-weight: var(--font-weight-bold);
      font-size: var(--text-3xl) !important;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      filter: drop-shadow(0 0 8px rgba(234, 88, 12, 0.6));
    }

    .stat-value {
      color: var(--text-primary) !important;
      font-weight: var(--font-weight-semibold);
    }

    .stat-label {
      color: var(--text-secondary) !important;
      font-weight: var(--font-weight-medium);
    }

    .player-avatar {
      border: 3px solid #ea580c !important;
      box-shadow: 0 4px 12px rgba(234, 88, 12, 0.5);
    }

    .team-info {
      background: rgba(234, 88, 12, 0.2) !important;
      border: 2px solid rgba(234, 88, 12, 0.5) !important;
      color: var(--text-primary) !important;
      font-weight: var(--font-weight-bold);

      &:hover {
        background: rgba(234, 88, 12, 0.3) !important;
        border-color: #ea580c !important;
        transform: scale(1.05);
      }
    }
  }

  .rank-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
    min-width: 80px;
    padding: var(--spacing-sm);

    .rank {
      font-size: var(--text-2xl);
      font-weight: var(--font-weight-bold);
      color: var(--text-primary);
      text-align: center;
      line-height: 1;
      background: rgba(255, 255, 255, 0.1);
      padding: var(--spacing-sm) var(--spacing-md);
      border-radius: var(--radius-full);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .crown-icon {
      font-size: var(--text-lg);
      color: #fbbf24;
      animation: crown-glow 3s ease-in-out infinite;
      filter: drop-shadow(0 0 8px rgba(251, 191, 36, 0.8));
      margin-top: var(--spacing-xs);
    }
  }

  .player-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);

    .player-avatar {
      width: 50px;
      height: 50px;
      border-radius: var(--radius-full);
      object-fit: cover;
      border: 2px solid var(--border-primary);
    }

    .player-details {
      .player-name {
        font-size: var(--text-lg);
        font-weight: var(--font-weight-bold);
        color: var(--text-primary);
        margin: 0 0 var(--spacing-xs) 0;
      }

      .player-position {
        font-size: var(--text-sm);
        color: var(--text-secondary);
        margin: 0;
      }
    }
  }

  .team-info {
    cursor: pointer;
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--surface-tertiary);
    border-radius: var(--radius-md);
    transition: all 0.2s ease;

    &:hover {
      background: var(--primary-100);
      color: var(--primary-700);
    }

    .team-name {
      font-size: var(--text-sm);
      font-weight: var(--font-weight-medium);
      color: var(--text-primary); // Changed from text-secondary for better contrast
    }
  }

  .stats-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);

    .primary-stat {
      text-align: center;

      .stat-value {
        display: block;
        font-size: var(--text-2xl);
        font-weight: var(--font-weight-bold);
        color: var(--primary-500);
        line-height: 1;
      }

      .stat-label {
        font-size: var(--text-sm);
        color: var(--text-secondary);
        font-weight: var(--font-weight-medium);
      }
    }

    .secondary-stats {
      display: flex;
      gap: var(--spacing-md);

      .stat-item {
        text-align: center;

        .stat-value {
          display: block;
          font-size: var(--text-base);
          font-weight: var(--font-weight-semibold);
          color: var(--text-primary);
          line-height: 1;
        }

        .stat-label {
          font-size: var(--text-xs);
          color: var(--text-secondary);
        }
      }
    }
  }

  @media (max-width: 768px) {
    grid-template-columns: auto 1fr;
    gap: var(--spacing-md);

    .team-info,
    .stats-section .secondary-stats {
      display: none;
    }

    .stats-section {
      gap: var(--spacing-sm);
    }
  }
}

/* === LOADING & EMPTY STATES === */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  color: var(--text-secondary);

  i {
    font-size: var(--text-2xl);
    margin-bottom: var(--spacing-md);
    color: var(--primary-500);
  }

  p {
    font-size: var(--text-lg);
    margin: 0;
  }
}

.no-data {
  text-align: center;
  padding: var(--spacing-xl);
  color: var(--text-secondary);

  i {
    font-size: var(--text-3xl);
    margin-bottom: var(--spacing-md);
    color: var(--text-tertiary);
  }

  p {
    font-size: var(--text-lg);
    margin: 0;
  }
}

/* === ANIMATIONS === */
@keyframes crown-glow {
  0% {
    opacity: 0.8;
    transform: scale(1) rotate(-2deg);
    filter: drop-shadow(0 0 8px rgba(251, 191, 36, 0.6));
  }
  50% {
    opacity: 1;
    transform: scale(1.15) rotate(2deg);
    filter: drop-shadow(0 0 12px rgba(251, 191, 36, 1));
  }
  100% {
    opacity: 0.8;
    transform: scale(1) rotate(-2deg);
    filter: drop-shadow(0 0 8px rgba(251, 191, 36, 0.6));
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* === RESPONSIVE ENHANCEMENTS === */
@media (max-width: 768px) {
  .player-row {
    &.first-place,
    &.second-place,
    &.third-place {
      .rank-section {
        min-width: 50px;

        .rank {
          font-size: var(--text-lg);
        }

        .crown-icon {
          font-size: var(--text-xs);
        }
      }
    }
  }
}
